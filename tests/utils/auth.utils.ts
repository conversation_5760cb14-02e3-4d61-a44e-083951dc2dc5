import { type Page } from '@playwright/test';

// Utility function to handle login with robust redirect handling
export async function performLoginWithRedirectHandling(page: Page) {
  console.log('Starting login process...');

  // Navigate to login page
  await page.goto('/login');
  await page.waitForTimeout(1000);

  // Fill login form
  await page.fill('input[name="username"]', 'admin');
  await page.waitForTimeout(1000);
  await page.fill('input[name="password"]', 'adminPW01!');
  await page.waitForTimeout(1000);

  // Submit form and handle multi-step redirect
  await page.click('button[type="submit"]');

  // Handle the redirect chain: /login → / → /chat_center
  console.log('Handling post-login redirect chain...');

  // Wait for the final destination with retry logic
  await waitForFinalDestination(page, '/chat_center');
}

// Utility function to wait for final destination with retry logic
export async function waitForFinalDestination(page: Page, expectedUrl: string, maxRetries = 10) {
  for (let i = 0; i < maxRetries; i++) {
    const currentUrl = page.url();
    console.log(`Attempt ${i + 1}: Current URL is ${currentUrl}`);
    
    if (currentUrl.includes(expectedUrl)) {
      console.log(`Successfully reached ${expectedUrl}`);
      return;
    }
    
    await page.waitForTimeout(1000);
  }
  
  throw new Error(`Failed to reach ${expectedUrl} after ${maxRetries} attempts`);
}